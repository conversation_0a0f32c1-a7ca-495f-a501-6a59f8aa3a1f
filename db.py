import asyncpg
import os
import uj<PERSON> as json
from dotenv import load_dotenv
import select
import requests
import asyncio
import websockets
from fastapi import FastAPI, WebSocket, Request
from fastapi.responses import HTMLResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Stream, Parameter, Say

load_dotenv()


class db_manager:

    def __init__(self):
        pass

    async def read_function_schema(self, pool: asyncpg.Pool, client_id: str):
        """Read function schema from database using asyncpg."""
        try:
            async with pool.acquire() as conn:
                query = """
                    SELECT function_name, function_schema, system_prompt, api_link, method_type
                    FROM chatbot.function_definitions
                    WHERE organisation_id = $1
                """
                result = await conn.fetch(query, client_id)
            #     print('Results:',results)
                await conn.close()
                if not result:
                    return None, None, None

                all_base_urls, all_function_names, all_methods = [], [], []
                function_tools = result[0][1]
                prompt = result[0][2]
                for row in result:
                    api_link = row['api_link']
                    function_name = row['function_name']
                    method = row['method_type']
                    all_base_urls.append(api_link)
                    all_function_names.append(function_name)
                    all_methods.append(method)

                function_tools = json.loads(function_tools)

                # Convert to the realtime api's required format
                converted_tools = []

                for tool in function_tools:
                    function_data = tool.get("function", {})
                    converted_tools.append({
                        "type": "function",
                        "name": function_data.get("name"),
                        "description": function_data.get("description"),
                        "parameters": function_data.get("parameters")
                    })
                    
                api_links = {i:[j,k] for i, j, k in zip(all_function_names, all_base_urls, all_methods)}

                return converted_tools, prompt, api_links

        except Exception as e:
            print(f"Database error: {e}")
            return None, None, None


    async def validate_secret(self, pool: asyncpg.Pool, org_id: str):
        """Authenticates the organisation using asyncpg."""
        try:
            async with pool.acquire() as conn:
                query = """
                    SELECT inbound_secret
                    FROM chatbot.organisations
                    WHERE organisation_id = $1
                    """
                secret = await conn.fetchval(query, org_id)
                return secret

        except Exception as e:
            print(f"Database error: {e}")
            return None



    # def listen(self):

    #     print('Begin...')

    #     conn = psycopg2.connect(**self.db_params)
    #     conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
    #     cur = conn.cursor()

    #     cur.execute("LISTEN new_job;")
    #     print("Listening for new jobs...")

    #     while True:
    #         select.select([conn], [], [])
    #         conn.poll()
    #         while conn.notifies:
    #             notify = conn.notifies.pop()
    #             print("Received notification:", notify.payload)
    #             url = "https://outbound-call-985795046666.us-east1.run.app/make_call/"
    #             data = {
    #                     "from_number": "+12314987814",
    #                     "to_number": "+923034445823"
    #                     }
    #             headers = {"Content-Type": "application/json"}


    #             response = requests.post(url, json=data, headers=headers)
    #             print('RESPONSE:',response)



