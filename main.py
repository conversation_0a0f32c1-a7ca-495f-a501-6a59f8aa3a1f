import requests, aiohttp, time
import ujson as json
import asyncio
import httpx, websockets
from fastapi import FastAPI, WebSocket, Request, Depends
from fastapi.responses import HTMLResponse
from fastapi import FastAPI, Request, HTTPException, Response
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Stream, Parameter, Say
from twilio.rest import Client
from dotenv import load_dotenv
from urllib.parse import parse_qs
import asyncpg
from datetime import datetime
import math
import logging
import base64
import os       
from pydantic import BaseModel, Field
from openai import OpenAI, InvalidWebhookSignatureError
from db import db_manager
from DBEngine import lifespan, get_pool, get_pool_ws
from decrypt import decrypt_aes_256_cbc
from prompts import get_voice_instruct, get_org_instruct
from tools import get_order_status_async, store_customer_complaint, complaint_status

load_dotenv()
db = db_manager()

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
PORT = int(os.getenv('PORT', 5050))
ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
DBWRAPPER_URL = os.getenv('DBWRAPPER_URL')
MODEL = os.getenv('MODEL')
VOICE = os.getenv('VOICE')
RAG_URL = os.getenv('RAG_URL')
LOG_EVENT_TYPES = ['error']

SHOW_TIMING_MATH = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


# --- FastAPI Application Instance ---
app = FastAPI(lifespan=lifespan)

if not OPENAI_API_KEY:
    raise ValueError('Missing the OpenAI API key. Please set it in the .env file.')

AUTH_HEADER = {"Authorization": "Bearer " + os.getenv("OPENAI_API_KEY")}

client = OpenAI()
# --- Pydantic Models for Type Hinting and Validation ---

class CallData(BaseModel):
    call_id: str = Field(..., alias="call_id")

class WebhookEvent(BaseModel):
    type: str
    data: CallData

@app.get("/")
async def index_page():
    return {"message": "Twilio Media Stream Server is running!"}

# --- Webhook Handling Logic ---
@app.post("/")
async def handle_webhook(request: Request):
    """
    Handles the incoming webhook from OpenAI to manage a new call.
    """
    # 1. Verify the webhook signature
    print(f'Request: {request}')
    try:
        payload = await request.body()
        event = client.webhooks.unwrap(payload, request.headers)
    except InvalidWebhookSignatureError:
        raise HTTPException(status_code=400, detail="Invalid webhook signature.")

    # 2. Process the 'realtime.call.incoming' event
    if event.type == "realtime.call.incoming":
        call_id = event.data.call_id
        print(f"📞 Incoming call received: {call_id}")

        # 3. Accept the call via OpenAI's REST API
        accept_url = f"https://api.openai.com/v1/realtime/calls/{call_id}/accept"
        call_config = {
            "type": "realtime",
            "instructions": "You are a helpful support agent. You have a bubbly and friendly personality."
            "Talk to the user and answer any questions.",
            "model": os.getenv("MODEL"),
        }
        
        async with httpx.AsyncClient() as http_client:
            response = await http_client.post(accept_url, headers=AUTH_HEADER, json=call_config)
            if response.status_code != 200:
                print(f"Error accepting call: {response.text}")
                raise HTTPException(status_code=500, detail="Failed to accept the call.")

        # 4. Start a background task to handle the WebSocket connection
        asyncio.create_task(websocket_task(call_id))
        
        # 5. Acknowledge the webhook with a 200 OK response
        return Response(status_code=200)

    return Response(status_code=200, content="Event received, but no action taken.")


async def websocket_task(call_id: str):
    """
    Connects to the OpenAI WebSocket to manage the live call.
    """
    websocket_uri = f"wss://api.openai.com/v1/realtime?call_id={call_id}"
    
    # Initial greeting message to send once connected

    initial_conversation_item = {
        "type": "conversation.item.create",
        "item": {
            "type": "message",
            "role": "user",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       "Greet the user with: 'Hi there! I am the whatsapp bot from Najoomie Technologies.'"
                        "Speak only in English."
                    )
                }
            ]
        }
    }

    initial_message = {
        "type": "response.create"
    }
    

    try:
        async with websockets.connect(websocket_uri, extra_headers=AUTH_HEADER) as websocket:
            print(f"WebSocket connected for call: {call_id}")
            await websocket.send(json.dumps(initial_conversation_item))
            await websocket.send(json.dumps(initial_message))

            # Listen for and print all subsequent events from the call
            async for message in websocket:
                print(f"WebSocket event for {call_id}: {message}")

    except Exception as e:
        print(f"WebSocket error for call {call_id}: {e}")

@app.api_route("/incoming-call", methods=["GET", "POST"])
async def handle_incoming_call(request: Request,
                                pool: asyncpg.Pool = Depends(get_pool)):
    """Handle incoming call and return TwiML response to connect to Media Stream."""
    response = VoiceResponse()
    response.say("Please wait while we connect your call to the AI voice assistant, powered by Najoomie Technologies")
    # response.play('https://najoomi-aws-storage.s3.us-east-1.amazonaws.com/testing/Kalimba-%5BAudioTrimmer.com%5D+(1).mp3', loop=1)

    caller_number = request.query_params.get("Called", "unknown")
    encrypted_data = request.query_params.get("data","N/A")

    try:
        org_id = await decrypt_aes_256_cbc(encrypted_data)
        org_id = org_id.split('=')[-1]

        secret = await db.validate_secret(pool, org_id)
        if secret and secret == encrypted_data:
            print(f'Organisation: {org_id}, Status: Valid')
        else:
            logger.warning(f"Failed to validate incoming call: Secret mismatch for org_id {org_id}")
            response = VoiceResponse()
            response.say("Sorry, your call could not be authenticated.")
            response.hangup()
            return HTMLResponse(content=str(response), media_type="application/xml")

    except Exception as e:
        logger.warning(f"Failed decrypting client secret: {e}")
        response = VoiceResponse()
        response.say("Sorry, there was an issue with your call. Please contact customer support.")
        response.hangup()
        return HTMLResponse(content=str(response), media_type="application/xml")

    host = request.url.hostname
    # Create the connect and stream objects
    connect = Connect()
    stream = Stream(url=f'wss://{host}/media-stream')
    stream.parameter(name="CallerNumber", value=org_id)

    connect.append(stream)
    response.append(connect)
    return HTMLResponse(content=str(response), media_type="application/xml")


@app.websocket("/media-stream")
async def handle_media_stream(
    websocket: WebSocket,
    pool: asyncpg.Pool = Depends(get_pool_ws) # Inject the pool here
    ):
    """Handle WebSocket connections between Twilio and OpenAI."""
    await websocket.accept()
    print("Client connected")
    # Connection specific state
    stream_sid = None
    latest_media_timestamp = 0
    last_assistant_item = None
    mark_queue = []
    response_start_timestamp_twilio = None
    dtmfs = None  # Global variable to store the latest DTMF input
    current_response_cumulative_duration_ms = 0


    async for message in websocket.iter_text():
        data = json.loads(message)
        if data.get("event") == 'start':
            stream_sid = data['start']['streamSid']
            call_sid   = data["start"]["callSid"]
            org_id = data.get("start", {}).get("customParameters", {}).get("CallerNumber", "unknown")
            break

    client = Client(ACCOUNT_SID, AUTH_TOKEN)

    client.calls(call_sid) \
        .recordings \
        .create(
            recording_channels="dual",
            recording_status_callback=f"{DBWRAPPER_URL}/inbound_status",
            recording_status_callback_event=["completed"],
            recording_status_callback_method='GET'
        )
    print(f"Started recording CallSid={call_sid}")
    function_tools, system_message, api_links  = await db.read_function_schema(pool, client_id=org_id)

    # Check if database function failed
    if function_tools is None or system_message is None or api_links is None:
        logger.warning(f"Failed to load function schema for org_id: {org_id}. Reverting to RAG only...")
        function_tools = [
            {
            "type": "function",
            "name": "handle_outof_scope_cases",
            "description": "Use this function as a final resort if no other function is suitable for the users query. This is the primary tool for answering general knowledge questions, open-ended inquiries, or any request that falls outside the scope of other specialized functions. It queries a comprehensive knowledge base to find an answer.",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_query": { "type": "string", "description": "The users original, complete question or topic to search for in the knowledge base."}
                },
                "required": ["user_query"]
            }
            }
            ]
        api_links = {}
        system_prompt = None
    
    additional_prompt = await get_voice_instruct('urdu')
    org_prompt = await get_org_instruct(client_id=org_id)
    system_message = org_prompt + '\n' + system_message + '\n' + additional_prompt 

    async with websockets.connect(
        f'wss://api.openai.com/v1/realtime?model={MODEL}',
        extra_headers={
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "OpenAI-Beta": "realtime=v1"
        }
    ) as openai_ws:
        print(f'Connected to Speech to Speech model: {MODEL}')
        await initialize_session(openai_ws,function_tools,system_message)


        async def receive_from_twilio():
            dtmf_list = []
              # Access the global variable
            """Receive audio data from Twilio and send it to the OpenAI Realtime API."""
            nonlocal stream_sid, latest_media_timestamp, dtmfs
            try:
                async for message in websocket.iter_text():
                    data = json.loads(message)
                    if data['event'] == 'media' and openai_ws.open:
                        latest_media_timestamp = int(data['media']['timestamp'])
                        audio_append = {
                            "type": "input_audio_buffer.append",
                            "audio": data['media']['payload']
                        }
                        await openai_ws.send(json.dumps(audio_append))
                    elif data['event'] == 'start':
                        stream_sid = data['start']['streamSid']
                        print(f"Incoming stream has started {stream_sid}")
                        response_start_timestamp_twilio = None
                        latest_media_timestamp = 0
                        last_assistant_item = None
                    elif data['event'] == 'mark':
                        if mark_queue:
                            mark_queue.pop(0)
                    elif data['event'] == 'dtmf' and openai_ws.open:
                        dtmf = data['dtmf']['digit']
                        if dtmf == '#':
                            dtmfs = ''.join(dtmf_list)
                            print(f'DTMF dialed: {dtmfs}')
                            dtmf_input = dtmfs
                            await send_dtmf_arguments(openai_ws, dtmfs)  # Send a message to OpenAI to let it know DTMF input is ready
                            dtmf_list.clear()
                        else:
                            dtmf_list.append(dtmf)

            except WebSocketDisconnect:
                print("Client disconnected.")
                if openai_ws.open:
                    await openai_ws.close()


        async def send_to_twilio():
            """Receive events from the OpenAI Realtime API, send audio back to Twilio."""
            nonlocal stream_sid, last_assistant_item, response_start_timestamp_twilio, dtmfs, current_response_cumulative_duration_ms
            try:
                async for openai_message in openai_ws:
                    response = json.loads(openai_message)
                    if response['type'] in LOG_EVENT_TYPES:
                        print(f"Received event: {response['type']}", response)

                    if response.get('type') == 'response.audio.delta' and 'delta' in response:
                        # Check if this is the start of a new item compared to the last one processed
                        new_item_id = response.get('item_id')
                        if new_item_id and new_item_id != last_assistant_item:
                            # Reset duration for the new item
                            current_response_cumulative_duration_ms = 0
                            last_assistant_item = new_item_id # Update last_assistant_item here
                            response_start_timestamp_twilio = None # Reset timestamp for the new response

                        # --- Send audio to Twilio ---
                        audio_payload = response['delta'] # Re-encode needed? Check if already base64
                        audio_delta = {
                            "event": "media",
                            "streamSid": stream_sid,
                            "media": {
                                "payload": audio_payload # Use the re-encoded payload
                            }
                        }
                        await websocket.send_json(audio_delta)
                        # Calculate duration of this delta
                        decoded_audio = base64.b64decode(response['delta'])
                        # Assuming 8kHz sample rate, 16-bit PCM (2 bytes/sample) or 8-bit mu-law (1 byte/sample)
                        # Twilio typically uses 8kHz mu-law (8000 bytes per second or 8 bytes per ms)
                        # Check OpenAI Realtime API docs for exact format if unsure. Let's assume 8kHz mu-law.
                        bytes_per_millisecond = 8
                        chunk_duration_ms = len(decoded_audio) / bytes_per_millisecond
                        current_response_cumulative_duration_ms += chunk_duration_ms
                        # --- Handle timestamp for latency calculation (if still needed elsewhere) ---
                        if response_start_timestamp_twilio is None:
                            # This timestamp is less critical now for truncation but might be useful for other latency metrics
                            response_start_timestamp_twilio = latest_media_timestamp
                            # if SHOW_TIMING_MATH:
                            #     print(f"Setting start timestamp for new response: {response_start_timestamp_twilio}ms")

                        # --- Send mark ---
                        await send_mark(websocket, stream_sid)

                    # Handle speech started events
                    if response.get('type') == 'input_audio_buffer.speech_started':
                        if last_assistant_item:
                            await handle_speech_started_event() # Pass the tracked duration

                    # Handling function calls
                    if response.get('type') == 'response.done':
                        for item in response['response']['output']:
                            if item['type'] == 'function_call':
                                arguments = json.loads(item['arguments'])
                                call_id = item["call_id"]
                                function_name = item['name']

                                result = None # Initialize result

                                logger.info(f"Function invoked: {function_name}, Arguments: {arguments}")
    
                                if function_name == 'handle_outof_scope_cases':
                                    # Handles RAG calls
                                    headers = {"Content-Type": "application/json"}
                                    payload = {"question": arguments.get('user_query'),"history": [],"organisation_id": org_id,"newPrompt": "","lead_gen": False,"voicemessage": False,"user_language": "en","provideindexname": "","providenamespace": "","agent_module_subscription": False,"user_data": [],"user_language_voice": None,"inboundcall": False}
                                    
                                    logger.info(f"RAG URL: {RAG_URL}")
                                    logger.info(f"Payload: {payload}")
                                    async with aiohttp.ClientSession() as session:
                                        try:
                                            async with session.post(RAG_URL, json=payload, headers=headers, timeout=aiohttp.ClientTimeout(total=7.0)) as response:
                                                response.raise_for_status() # Raise for 4xx/5xx
                                                result_json = await response.json()
                                                result = result_json['text']
                                                logger.info(f"Response: {result}")
                                        except aiohttp.ClientError as exc:
                                            logger.error(f"HTTP request failed for {function_name} to {RAG_URL}: {exc}")
                                    

                                elif function_name == 'get_order_status' and org_id == 'TCS':
                                    result = await get_order_status_async(arguments, pool)

                                elif function_name == 'register_complaint' and org_id == 'TCS':
                                    result = await store_customer_complaint(arguments, pool)

                                elif function_name == 'complaint_status' and org_id == 'TCS':
                                    result = await complaint_status(arguments, pool)

                                else:

                                    if len(api_links) == 1:
                                        api_link, method = next(iter(api_links.values()))
                                    else:
                                        api_link, method = api_links[function_name][0], api_links[function_name][1]


                                    logger.info(f"External function invoked: {function_name}, Method: {method}")
                                    headers = {"Content-Type": "application/json"}
                                    logger.info(f"Request URL: {api_link}, Arguments: {arguments}")
                                    
                                    async with aiohttp.ClientSession() as session:
                                        try:

                                            if method == 'POST':
                                                async with session.post(api_link, json=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")
                                            elif method == 'GET':
                                                async with session.get(api_link, params=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")
                                            elif method == 'PUT':
                                                async with session.put(api_link, json=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")
                                            elif method == 'DELETE':
                                                async with session.delete(api_link, json=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")
                                            elif method == 'PATCH':
                                                async with session.patch(api_link, json=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")

                                        except aiohttp.ClientError as exc:
                                            logger.error(f"HTTP request failed for {function_name} to {api_link}: {exc}")


                                if dtmfs:
                                    delete_dtmf_message = {
                                        "type": "conversation.item.delete",  #This should remove any dtmfs in the current context
                                        "item_id": "msg_001"
                                    }

                                    await openai_ws.send(json.dumps(delete_dtmf_message))
                                    dtmfs = None
                                    print('Deleted dtmf in conversation context')


                                function_output = json.dumps(result)
                                # logger.info(f"Function output: {function_output}")
                                response_message = {
                                    "type": "conversation.item.create",
                                    "item": {
                                        "type": "function_call_output",
                                        "output": function_output,
                                        "call_id": call_id
                                    }
                                }
                                await openai_ws.send(json.dumps(response_message))
                                await openai_ws.send(json.dumps({"type": "response.create"}))

            except WebSocketDisconnect:
                    logger.info('Error: Websocket Disconnected')
            except Exception as e:
                logger.error(f"Error in send_to_twilio: {e}")

        async def handle_speech_started_event():
            """Handle interruption when the caller's speech starts."""
            nonlocal response_start_timestamp_twilio, last_assistant_item
            nonlocal current_response_cumulative_duration_ms, stream_sid, mark_queue, websocket, openai_ws # Ensure all needed nonlocals are listed

            if mark_queue and last_assistant_item: # Check mark_queue and item_id are valid
                # Use the tracked cumulative duration directly, flooring it to be safe.
                truncation_point_ms = math.floor(current_response_cumulative_duration_ms)

                # Ensure truncation point is not negative (shouldn't happen with cumulative, but safe)
                truncation_point_ms = max(0, truncation_point_ms)

                print(f"Interrupting item {last_assistant_item} at tracked duration {current_response_cumulative_duration_ms:.2f}ms (sending truncate at {truncation_point_ms}ms)")

                truncate_event = {
                    "type": "conversation.item.truncate",
                    "item_id": last_assistant_item,
                    "content_index": 0, # Assuming single content item (audio)
                    "audio_end_ms": truncation_point_ms # Use the floored calculated point without buffer
                }
                try:
                    await openai_ws.send(json.dumps(truncate_event))
                    print(f"Sent truncate event for item {last_assistant_item} at {truncation_point_ms}ms")
                except Exception as e:
                    print(f"Error sending truncate event: {e}")
                    # Decide if you should still clear Twilio queue etc. even if truncate fails

                # Clear Twilio's playback buffer
                print(f"Sending clear event to Twilio for stream {stream_sid}")
                await websocket.send_json({
                    "event": "clear",
                    "streamSid": stream_sid
                })

                # Reset state after handling interruption
                print("Resetting state after interruption.")
                mark_queue.clear()
                last_assistant_item = None
                response_start_timestamp_twilio = None # If you still use this elsewhere
                current_response_cumulative_duration_ms = 0 # Reset the duration tracker

            # else:
                # print("Speech started detected, but no active response item or mark queue to interrupt.")

        async def send_mark(connection, stream_sid):
            if stream_sid:
                mark_event = {
                    "event": "mark",
                    "streamSid": stream_sid,
                    "mark": {"name": "responsePart"}
                }
                await connection.send_json(mark_event)
                mark_queue.append('responsePart')

        await asyncio.gather(receive_from_twilio(), send_to_twilio())

async def send_dtmf_arguments(openai_ws, dtmfs):
    """Send Dial Pad Input to the model"""
    dtmf_input_json = {
        "type": "conversation.item.create",
        "item": {
            "id":"msg_001",
            "type": "message",
            "role": "system",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       f"The user entered: {dtmfs} via keypad. Call the relevant function and use these as function call arguments."
                    )
                }
            ]
        }
    }

    await openai_ws.send(json.dumps(dtmf_input_json))

async def send_initial_conversation_item(openai_ws):
    """Send initial conversation item if AI talks first."""
    initial_conversation_item = {
        "type": "conversation.item.create",
        "item": {
            "type": "message",
            "role": "user",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       "Greet the user in Urdu with 'As-salamu alaykum! I am an AI voice assistant powered by Najoomie Technologies."
                        "How may I help you?"
                        "Do not use 'Wa alaykum as-salam as a greeting."
                    )
                }
            ]
        }
    }
    await openai_ws.send(json.dumps(initial_conversation_item))
    await openai_ws.send(json.dumps({"type": "response.create"}))


async def handle_outof_scope_cases(openai_ws,arguments,call_id):
    pass

async def initialize_session(openai_ws,function_tools,system_message):
    """Control initial session with OpenAI."""
    session_update = {
        "type": "session.update",
        "session": {
            "turn_detection": {
                "type": "server_vad",
                "threshold": 0.6,
                "prefix_padding_ms": 300,
                "silence_duration_ms": 300,
                "create_response": True,
                },
            "input_audio_noise_reduction": {"type":"near_field"},
            "input_audio_format": "g711_ulaw",
            "output_audio_format": "g711_ulaw",
            "voice": VOICE,
            "instructions": system_message,
            "speed": 1.2,
            "modalities": ["text", "audio"],
            "temperature": 0.8,
            "tools": function_tools,
            "tool_choice": "auto"

        }}

    await openai_ws.send(json.dumps(session_update))
    # Uncomment the next line to have the AI speak first
    await send_initial_conversation_item(openai_ws)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)
    # db.read_function_schema(client_id='TCS')


