#!/bin/bash

# Deployment script for inbound calling

set -e

# Configuration
IMAGE_NAME="inbound"
TAG=${1:-latest}
PLATFORM=${2:-"gcp"}

echo "Deploying Inbound Calling Service"
echo "Platform: ${PLATFORM}"
echo "Image: ${IMAGE_NAME}:${TAG}"

case $PLATFORM in
    "docker")
        echo "Deploying with Docker Compose..."
        docker-compose down
        docker-compose up -d
        echo "Application deployed at: http://localhost:8080"
        ;;

    "aws")
        echo "Deploying to AWS ECS..."
        # Build and push to ECR
        AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        AWS_REGION=${AWS_REGION:-us-east-1}
        ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

        # Login to ECR
        aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${ECR_REGISTRY}

        # Create repository if it doesn't exist
        aws ecr describe-repositories --repository-names ${IMAGE_NAME} --region ${AWS_REGION} || \
        aws ecr create-repository --repository-name ${IMAGE_NAME} --region ${AWS_REGION}

        # Build and push
        docker build -t ${IMAGE_NAME}:${TAG} .
        docker tag ${IMAGE_NAME}:${TAG} ${ECR_REGISTRY}/${IMAGE_NAME}:${TAG}
        docker push ${ECR_REGISTRY}/${IMAGE_NAME}:${TAG}

        echo "Image pushed to ECR: ${ECR_REGISTRY}/${IMAGE_NAME}:${TAG}"
        echo "Update your ECS service to use this image"
        ;;

    "gcp")
        echo "Deploying to Google Cloud Run..."
        PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}

        # Build and push to Container Registry
        docker build -t gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG} .
        docker push gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG}

        # Deploy to Cloud Run
        gcloud run deploy ${IMAGE_NAME} \
            --image gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG} \
            --platform managed \
            --region us-central1 \
            --allow-unauthenticated \
            --port 5050 \
            --memory 32Gi \
            --cpu 8 \
            --execution-environment gen2 \
            --min-instances 1 \
            --max-instances 100 \
            --cpu-boost \
            --concurrency 80
        ;;

    "azure")
        echo "Deploying to Azure Container Instances..."
        RESOURCE_GROUP=${AZURE_RESOURCE_GROUP:-webrtc-frontend-rg}
        REGISTRY_NAME=${AZURE_REGISTRY_NAME:-webrtcfrontendregistry}

        # Build and push to ACR
        az acr build --registry ${REGISTRY_NAME} --image ${IMAGE_NAME}:${TAG} .

        # Deploy to Container Instances
        az container create \
            --resource-group ${RESOURCE_GROUP} \
            --name ${IMAGE_NAME} \
            --image ${REGISTRY_NAME}.azurecr.io/${IMAGE_NAME}:${TAG} \
            --dns-name-label ${IMAGE_NAME}-$(date +%s) \
            --ports 80
        ;;

    "heroku")
        echo "Deploying to Heroku..."
        # Login to Heroku Container Registry
        heroku container:login

        # Build and push
        heroku container:push web --app ${HEROKU_APP_NAME}
        heroku container:release web --app ${HEROKU_APP_NAME}
        ;;

    *)
        echo "Unknown platform: ${PLATFORM}"
        echo "Supported platforms: docker, aws, gcp, azure, heroku"
        exit 1
        ;;
esac

echo "Deployment completed!"
