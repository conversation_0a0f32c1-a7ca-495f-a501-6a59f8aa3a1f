// File: test_ipcom.js
'use strict';

// Note the change in how we import the client
const { AriClient } = require('@ipcom/asterisk-ari');

console.log('--- Starting Minimal Connection Test with @ipcom/asterisk-ari ---');

// The client is configured with an object instead of separate arguments
const client = new AriClient({
  host: 'ari.nayatel.com/ari',
  port: 443, // Standard HTTPS/WSS port
  username: 'ariuser',
  password: 'ariaitesting',
  secure: true, // This tells the client to use HTTPS and WSS
  rejectUnauthorized: false // This bypasses the SSL certificate check
});

async function runTest() {
  try {
    console.log('Attempting to connect to ARI WebSocket...');

    // The connect method is different for this library
    await client.connectWebSocket(['stasis_app']);

    console.log('✅✅✅ SUCCESS: WebSocket connection established!');

    client.on('StasisStart', (event) => {
      console.log(`StasisStart event for ${event.channel.name}. The connection is fully working.`);
    });

    console.log('Listening for calls...');

  } catch (err) {
    console.error('❌❌❌ FAILED: The connection threw an error.');
    console.error(err);
  }
}

runTest();

process.on('unhandledRejection', (reason, promise) => {
  console.error('🔥🔥🔥 FATAL: An unhandled rejection occurred!');
  console.error('Reason:', reason);
});