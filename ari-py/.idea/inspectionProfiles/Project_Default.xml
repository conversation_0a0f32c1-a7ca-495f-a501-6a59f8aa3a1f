<component name="InspectionProjectProfileManager">
  <profile version="1.0" is_locked="false">
    <option name="myName" value="Project Default" />
    <option name="myLocal" value="false" />
    <inspection_tool class="PyDocstringInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="httpretty.*" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="SpellCheckingInspection" enabled="true" level="TYPO" enabled_by_default="true">
      <option name="processCode" value="false" />
      <option name="processLiterals" value="false" />
      <option name="processComments" value="true" />
    </inspection_tool>
  </profile>
</component>