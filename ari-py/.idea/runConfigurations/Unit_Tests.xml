<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Unit Tests" type="tests" factoryName="Nosetests">
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs />
    <option name="SDK_HOME" value="$USER_HOME$/virtualenv/swagger/bin/python" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="IS_MODULE_SDK" value="false" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <module name="ast-ari-py" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" enabled="false" sample_coverage="true" runner="coverage.py" />
    <option name="SCRIPT_NAME" value="" />
    <option name="CLASS_NAME" value="" />
    <option name="METHOD_NAME" value="" />
    <option name="FOLDER_NAME" value="$PROJECT_DIR$/ari_test" />
    <option name="TEST_TYPE" value="TEST_FOLDER" />
    <option name="PATTERN" value="" />
    <option name="USE_PATTERN" value="false" />
    <option name="PARAMS" value="--config nose.cfg" />
    <option name="USE_PARAM" value="true" />
    <RunnerSettings RunnerId="PyDebugRunner" />
    <RunnerSettings RunnerId="PythonCover" />
    <RunnerSettings RunnerId="PythonRunner" />
    <ConfigurationWrapper RunnerId="PyDebugRunner" />
    <ConfigurationWrapper RunnerId="PythonCover" />
    <ConfigurationWrapper RunnerId="PythonRunner" />
    <method />
  </configuration>
</component>