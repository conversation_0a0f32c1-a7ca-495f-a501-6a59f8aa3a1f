{"_copyright": "Copyright (C) 2012 - 2013, Digium, Inc.", "_author": "<PERSON>, II <<EMAIL>>", "apiVersion": "0.0.0-test", "swaggerVersion": "1.1", "basePath": "http://ari.py/ari", "resourcePath": "/api-docs/recordings.{format}", "apis": [{"path": "/recordings/stored", "description": "Recordings", "operations": [{"httpMethod": "GET", "summary": "List recordings that are complete.", "nickname": "listStored", "responseClass": "List[StoredRecording]"}]}, {"path": "/recordings/stored/{recordingName}", "description": "Individual recording", "operations": [{"httpMethod": "GET", "summary": "Get a stored recording's details.", "nickname": "getStored", "responseClass": "StoredRecording", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}]}, {"httpMethod": "DELETE", "summary": "Delete a stored recording.", "nickname": "deleteStored", "responseClass": "void", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}]}]}, {"path": "/recordings/live/{recordingName}", "description": "A recording that is in progress", "operations": [{"httpMethod": "GET", "summary": "List live recordings.", "nickname": "getLive", "responseClass": "LiveRecording", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}]}, {"httpMethod": "DELETE", "summary": "Stop a live recording and discard it.", "nickname": "cancel", "responseClass": "void", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}]}]}, {"path": "/recordings/live/{recordingName}/stop", "operations": [{"httpMethod": "POST", "summary": "Stop a live recording and store it.", "nickname": "stop", "responseClass": "void", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}]}]}, {"path": "/recordings/live/{recordingName}/pause", "operations": [{"httpMethod": "POST", "summary": "Pause a live recording.", "notes": "Pausing a recording suspends silence detection, which will be restarted when the recording is unpaused. Paused time is not included in the accounting for maxDurationSeconds.", "nickname": "pause", "responseClass": "void", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}, {"code": 409, "reason": "Recording not in session"}]}, {"httpMethod": "DELETE", "summary": "Unpause a live recording.", "nickname": "unpause", "responseClass": "void", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}, {"code": 409, "reason": "Recording not in session"}]}]}, {"path": "/recordings/live/{recordingName}/mute", "operations": [{"httpMethod": "POST", "summary": "Mute a live recording.", "notes": "Muting a recording suspends silence detection, which will be restarted when the recording is unmuted.", "nickname": "mute", "responseClass": "void", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}, {"code": 409, "reason": "Recording not in session"}]}, {"httpMethod": "DELETE", "summary": "Unmute a live recording.", "nickname": "unmute", "responseClass": "void", "parameters": [{"name": "recordingName", "description": "The name of the recording", "paramType": "path", "required": true, "allowMultiple": false, "dataType": "string"}], "errorResponses": [{"code": 404, "reason": "Recording not found"}, {"code": 409, "reason": "Recording not in session"}]}]}], "models": {"StoredRecording": {"id": "StoredRecording", "description": "A past recording that may be played back.", "properties": {"name": {"required": true, "type": "string"}, "format": {"required": true, "type": "string"}}}, "LiveRecording": {"id": "LiveRecording", "description": "A recording that is in progress", "properties": {"name": {"required": true, "type": "string", "description": "Base name for the recording"}, "format": {"required": true, "type": "string"}, "state": {"required": true, "type": "string"}}}}}