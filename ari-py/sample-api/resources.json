{"_copyright": "Copyright (C) 2012 - 2013, Digium, Inc.", "_author": "<PERSON>, II <<EMAIL>>", "apiVersion": "0.0.0-test", "swaggerVersion": "1.1", "basePath": "http://ari.py/ari", "apis": [{"path": "/api-docs/asterisk.{format}", "description": "Asterisk resources"}, {"path": "/api-docs/endpoints.{format}", "description": "Endpoint resources"}, {"path": "/api-docs/channels.{format}", "description": "Channel resources"}, {"path": "/api-docs/bridges.{format}", "description": "Bridge resources"}, {"path": "/api-docs/recordings.{format}", "description": "Recording resources"}, {"path": "/api-docs/sounds.{format}", "description": "Sound resources"}, {"path": "/api-docs/mailboxes.{format}", "description": "Mailbox (MWI) resources"}, {"path": "/api-docs/playbacks.{format}", "description": "Playback control resources"}, {"path": "/api-docs/deviceStates.{format}", "description": "Device state resources"}, {"path": "/api-docs/events.{format}", "description": "WebSocket resource"}, {"path": "/api-docs/applications.{format}", "description": "Stasis application resources"}]}