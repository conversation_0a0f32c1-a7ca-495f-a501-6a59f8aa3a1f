import os
import asyncio
import httpx
import websockets
import json
from fastapi import FastAP<PERSON>, Request, HTTPException, Response
from openai import OpenAI, InvalidWebhookSignatureError
from pydantic import BaseModel, Field
from dotenv import load_dotenv


load_dotenv()


# --- Configuration & Initialization ---
PORT = os.getenv("PORT", 5050)
app = FastAPI()

# Initialize the OpenAI client
# It automatically reads the OPENAI_API_KEY and OPENAI_WEBHOOK_SECRET from environment variables
AUTH_HEADER = {"Authorization": "Bearer " + os.getenv("OPENAI_API_KEY")}

client = OpenAI()

# --- Pydantic Models for Type Hinting and Validation ---
class CallData(BaseModel):
    call_id: str = Field(..., alias="call_id")

class WebhookEvent(BaseModel):
    type: str
    data: CallData

@app.get("/")
async def index_page():
    return {"message": "SIP Server is running!"}

# --- Webhook Handling Logic ---
@app.post("/")
async def handle_webhook(request: Request):
    """
    Handles the incoming webhook from OpenAI to manage a new call.
    """
    # 1. Verify the webhook signature
    print(f'Request: {request}')
    try:
        payload = await request.body()
        event = client.webhooks.unwrap(payload, request.headers)
    except InvalidWebhookSignatureError:
        raise HTTPException(status_code=400, detail="Invalid webhook signature.")

    # 2. Process the 'realtime.call.incoming' event
    if event.type == "realtime.call.incoming":
        call_id = event.data.call_id
        print(f"📞 Incoming call received: {call_id}")

        # 3. Accept the call via OpenAI's REST API
        accept_url = f"https://api.openai.com/v1/realtime/calls/{call_id}/accept"
        call_config = {
            "type": "realtime",
            "instructions": "You are a helpful support agent. You have a bubbly and friendly personality. "
            "Talk to the user and answer any questions.",
            "model": os.getenv("MODEL"),
        }
        
        async with httpx.AsyncClient() as http_client:
            response = await http_client.post(accept_url, headers=AUTH_HEADER, json=call_config)
            if response.status_code != 200:
                print(f"Error accepting call: {response.text}")
                raise HTTPException(status_code=500, detail="Failed to accept the call.")

        # 4. Start a background task to handle the WebSocket connection
        asyncio.create_task(websocket_task(call_id))
        
        # 5. Acknowledge the webhook with a 200 OK response
        return Response(status_code=200)

    return Response(status_code=200, content="Event received, but no action taken.")


async def websocket_task(call_id: str):
    """
    Connects to the OpenAI WebSocket to manage the live call.
    """
    websocket_uri = f"wss://api.openai.com/v1/realtime?call_id={call_id}"
    
    # Initial greeting message to send once connected
    

    initial_conversation_item = {
        "type": "conversation.item.create",
        "item": {
            "type": "message",
            "role": "user",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       "Greet the user with: 'Hi there! I am the whatsapp bot from Najoomie Technologies.'"
                        "Speak only in English."
                    )
                }
            ]
        }
    }

    initial_message = {
        "type": "response.create"
    }
    

    try:
        async with websockets.connect(websocket_uri, extra_headers=AUTH_HEADER) as websocket:
            print(f"WebSocket connected for call: {call_id}")
            await websocket.send(json.dumps(initial_conversation_item))
            await websocket.send(json.dumps(initial_message))

            # Listen for and print all subsequent events from the call
            async for message in websocket:
                print(f"WebSocket event for {call_id}: {message}")

    except Exception as e:
        print(f"WebSocket error for call {call_id}: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)