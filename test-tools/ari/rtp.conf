;
; RTP Configuration
;
[general]
;
; RTP start and RTP end configure start and end addresses
;
; Defaults are rtpstart=5000 and rtpend=31000
;
rtpstart=10000
rtpend=20000
;
; Whether to enable or disable UDP checksums on RTP traffic
;
;rtpchecksums=no
;
; The amount of time a DTMF digit with no 'end' marker should be
; allowed to continue (in 'samples', 1/8000 of a second)
;
;dtmftimeout=3000
; rtcpinterval = 5000 	; Milliseconds between rtcp reports
			;(min 500, max 60000, default 5000)
;
; Enable strict RTP protection.  This will drop RTP packets that do not come
; from the recognized source of the RTP stream.  Strict RTP qualifies RTP
; packet stream sources before accepting them upon initial connection and
; when the connection is renegotiated (e.g., transfers and direct media).
; Initial connection and renegotiation starts a learning mode to qualify
; stream source addresses.  Once Asterisk has recognized a stream it will
; allow other streams to qualify and replace the current stream for 5
; seconds after starting learning mode.  Once learning mode completes the
; current stream is locked in and cannot change until the next
; renegotiation.
; Valid options are "no" to disable strictrtp, "yes" to enable strictrtp,
; and "seqno", which does the same thing as strictrtp=yes, but only checks
; to make sure the sequence number is correct rather than checking the time
; interval as well.
; This option is enabled by default.
; strictrtp=yes
;
; Number of packets containing consecutive sequence values needed
; to change the RTP source socket address. This option only comes
; into play while using strictrtp=yes. Consider changing this value
; if rtp packets are dropped from one or both ends after a call is
; connected. This option is set to 4 by default.
; probation=8
;
; Enable sRTP replay protection. Buggy SIP user agents (UAs) reset the
; sequence number (RTP-SEQ) on a re-INVITE, for example, with Session Timers
; or on Call Hold/Resume, but keep the synchronization source (RTP-SSRC). If
; the new RTP-SEQ is higher than the previous one, the call continues if the
; roll-over counter (sRTP-ROC) is zero (the call lasted less than 22 minutes).
; In all other cases, the call faces one-way audio or even no audio at all.
; "replay check failed (index too old)" gets printed continuously. This is a
; software bug. You have to report this to the creator of that UA. Until it is
; fixed, you could disable sRTP replay protection (see RFC 3711 section 3.3.2).
; This option is enabled by default.
; srtpreplayprotection=yes
;
; Whether to enable or disable ICE support. This option is enabled by default.
; icesupport=false
;
; Hostname or address for the STUN server used when determining the external
; IP address and port an RTP session can be reached at. The port number is
; optional. If omitted the default value of 3478 will be used. This option is
; disabled by default. Name resolution will occur at load time, and if DNS is
; used, name resolution will occur repeatedly after the TTL expires.
;
; e.g. stundaddr=mystun.server.com:3478
;
; stunaddr=
;
; Some multihomed servers have IP interfaces that cannot reach the STUN
; server specified by stunaddr.  Blacklist those interface subnets from
; trying to send a STUN packet to find the external IP address.
; Attempting to send the STUN packet needlessly delays processing incoming
; and outgoing SIP INVITEs because we will wait for a response that can
; never come until we give up on the response.
; * Multiple subnets may be listed.
; * Blacklisting applies to IPv4 only.  STUN isn't needed for IPv6.
; * Blacklisting applies when binding RTP to specific IP addresses and not
; the wildcard 0.0.0.0 address.  e.g., A PJSIP endpoint binding RTP to a
; specific address using the bind_rtp_to_media_address and media_address
; options.  Or the PJSIP endpoint specifies an explicit transport that binds
; to a specific IP address.  Blacklisting is done via ACL infrastructure
; so it's possible to whitelist as well.
;
; stun_acl = named_acl
; stun_deny = 0.0.0.0/0
; stun_permit = *******/32
;
; For historic reasons stun_blacklist is an alias for stun_deny.
;
; Whether to report the PJSIP version in a SOFTWARE attribute for all
; outgoing STUN packets. This option is enabled by default.
;
; stun_software_attribute=yes
;
; Hostname or address for the TURN server to be used as a relay. The port
; number is optional. If omitted the default value of 3478 will be used.
; This option is disabled by default.
;
; e.g. turnaddr=myturn.server.com:34780
;
; turnaddr=
;
; Username used to authenticate with TURN relay server.
; turnusername=
;
; Password used to authenticate with TURN relay server.
; turnpassword=
;
; An ACL can be used to determine which discovered addresses to include for
; ICE, srflx and relay discovery.  This is useful to optimize the ICE process
; where a system has multiple host address ranges and/or physical interfaces
; and certain of them are not expected to be used for RTP. For example, VPNs
; and local interconnections may not be suitable or necessary for ICE. Multiple
; subnets may be listed. If left unconfigured, all discovered host addresses
; are used.
;
; ice_acl = named_acl
; ice_deny = 0.0.0.0/0
; ice_permit = *******/32
;
; For historic reasons ice_blacklist is an alias for ice_deny.
;
; The MTU to use for DTLS packet fragmentation. This option is set to 1200
; by default. The minimum MTU is 256.
; dtls_mtu = 1200
;
[ice_host_candidates]
;
; When Asterisk is behind a static one-to-one NAT and ICE is in use, ICE will
; expose the server's internal IP address as one of the host candidates.
; Although using STUN (see the 'stunaddr' configuration option) will provide a
; publicly accessible IP, the internal IP will still be sent to the remote
; peer. To help hide the topology of your internal network, you can override
; the host candidates that Asterisk will send to the remote peer.
;
; IMPORTANT: Only use this functionality when your Asterisk server is behind a
; one-to-one NAT and you know what you're doing. If you do define anything
; here, you almost certainly will NOT want to specify 'stunaddr' or 'turnaddr'
; above.
;
; The format for these overrides is:
;
;    <local address> => <advertised address>,[include_local_address]
;
; The following will replace ************ with ******* during ICE
; negotiation:
;
;************ => *******
;
; The following will include BOTH ************ and ******* during ICE
; negotiation instead of replacing ************.  This can make it easier
; to serve both local and remote clients.
;
;************ => *******,include_local_address
;
; You can define an override for more than 1 interface if you have a multihomed
; server. Any local interface that is not matched will be passed through
; unaltered. Both IPv4 and IPv6 addresses are supported.
