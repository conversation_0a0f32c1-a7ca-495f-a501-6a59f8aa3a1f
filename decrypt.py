from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.padding import PKCS7
from cryptography.hazmat.backends import default_backend
import os, base64
from dotenv import load_dotenv

load_dotenv()

ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

async def decrypt_aes_256_cbc(encrypted):
    key = ENCRYPTION_KEY.encode("utf-8")
    # Split the IV and ciphertext
    iv, ciphertext = encrypted.split(":")
    iv = base64.b64decode(iv)
    ciphertext = base64.b64decode(ciphertext)

    # Ensure the key is 32 bytes
    if len(key) != 32:
        raise ValueError("Key must be 32 bytes for AES-256.")

    # Create a cipher object and decrypt the data
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    decryptor = cipher.decryptor()
    padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()

    # Unpad the plaintext
    unpadder = PKCS7(algorithms.AES.block_size).unpadder()
    plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()


    return plaintext.decode()


