# Use the official Python image as the base image
FROM python:3.9.7
# Set the working directory in the container

WORKDIR / voice

# Copy the requirements file into the container
COPY requirements.txt .

# Install the required packages
RUN pip install -r requirements.txt

# Copy the rest of the application code into the container
COPY . .

EXPOSE 5050

# Command to run the Flask app
CMD ["python3", "main.py"]
