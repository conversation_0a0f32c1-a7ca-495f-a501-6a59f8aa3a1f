import asyncpg
import os
import logging
from contextlib import asynccontextmanager
from fastapi import Request, WebSocket, HTTPException, WebSocketException, status

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app):
    """
    FastAPI lifespan context manager for database connection pool management.
    Handles startup and shutdown of the database connection pool.
    """
    logger.info("Application startup: Initializing database connection pool...")
    try:
        # Create pool using env vars or db_config
        app.state.db_pool = await asyncpg.create_pool(
            user=os.getenv('user'),
            password=os.getenv('password'),
            database=os.getenv('dbname'),
            host=os.getenv('host'),
            port=os.getenv('port'),
            min_size=1,
            max_size=10
        )
        logger.info("Database connection pool created successfully.")
    except Exception as e:
        logger.critical(f"CRITICAL: Failed to create database connection pool during startup: {e}", exc_info=True)
        # App can start, but DB features will fail until DB is up.
        app.state.db_pool = None  # Ensure state is None if creation failed

    yield  # Application runs here

    # === Shutdown ===
    logger.info("Application shutdown: Closing database connection pool...")
    pool_to_close = getattr(app.state, "db_pool", None)
    if pool_to_close:
        try:
            await pool_to_close.close()
            logger.info("Database connection pool closed.")
        except Exception as e:
            logger.error(f"Failed to close database connection pool properly: {e}")


async def get_pool(request: Request) -> asyncpg.Pool:
    """
    Dependency function to get the database pool for HTTP requests.
    
    Args:
        request: FastAPI Request object
        
    Returns:
        asyncpg.Pool: Database connection pool
        
    Raises:
        HTTPException: If database pool is not available
    """
    pool = getattr(request.app.state, "db_pool", None)
    if pool is None:
        logger.error("Database pool is not available (HTTP context).")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database service is currently unavailable."
        )
    return pool


async def get_pool_ws(websocket: WebSocket) -> asyncpg.Pool:
    """
    Dependency function to get the database pool for WebSocket connections.
    
    Args:
        websocket: FastAPI WebSocket object
        
    Returns:
        asyncpg.Pool: Database connection pool
        
    Raises:
        WebSocketException: If database pool is not available
    """
    pool = getattr(websocket.app.state, "db_pool", None)
    if pool is None:
        logger.error("Database pool is not available (WebSocket context).")
        # Raise specific exception to signal connection failure due to DB issue
        raise WebSocketException(
            code=status.WS_1011_INTERNAL_ERROR,
            reason="Database service unavailable."
        )
    return pool


def configure_database_pool(
    user: str = None,
    password: str = None,
    database: str = None,
    host: str = None,
    port: str = None,
    min_size: int = 1,
    max_size: int = 10
) -> dict:
    """
    Configure database connection parameters.
    
    Args:
        user: Database username (defaults to env var 'user')
        password: Database password (defaults to env var 'password')
        database: Database name (defaults to env var 'dbname')
        host: Database host (defaults to env var 'host')
        port: Database port (defaults to env var 'port')
        min_size: Minimum pool size
        max_size: Maximum pool size
        
    Returns:
        dict: Database configuration parameters
    """
    return {
        'user': user or os.getenv('user'),
        'password': password or os.getenv('password'),
        'database': database or os.getenv('dbname'),
        'host': host or os.getenv('host'),
        'port': port or os.getenv('port'),
        'min_size': min_size,
        'max_size': max_size
    }


async def create_database_pool(**config) -> asyncpg.Pool:
    """
    Create a database connection pool with the given configuration.
    
    Args:
        **config: Database configuration parameters
        
    Returns:
        asyncpg.Pool: Database connection pool
        
    Raises:
        Exception: If pool creation fails
    """
    return await asyncpg.create_pool(**config)


async def close_database_pool(pool: asyncpg.Pool) -> None:
    """
    Safely close a database connection pool.
    
    Args:
        pool: Database connection pool to close
    """
    if pool:
        try:
            await pool.close()
            logger.info("Database connection pool closed successfully.")
        except Exception as e:
            logger.error(f"Failed to close database connection pool: {e}")
